package UI.post
{
   import UI.api.shop.ShopBuyObject;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.AppNormalUI;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.loadBar.LoadBar;
   import UI.shop.GoodsBox;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.PriceType;
   import dataAll._app.post.PostData;
   import dataAll._app.post.define.PostDefine;
   import flash.display.DisplayObject;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class PostUI extends AppNormalUI
   {
      
      private var closeBtn:SimpleButton;
      
      private var postTxt:TextField;
      
      private var timeTxt:TextField;
      
      private var expTxt:TextField;
      
      private var lvTxt:TextField;
      
      private var gripTag:Sprite;
      
      private var shopBox:GoodsBox = new GoodsBox();
      
      private var listNameArr:Array = ["day","level","privilege","pro"];
      
      private var boxObj:Object = {};
      
      private var dayBox:PostDayBox;
      
      private var levelBox:PostDayBox;
      
      private var privilegeBox:PostDayBox;
      
      private var proBox:PostDayBox;
      
      private var loadBarSp:Sprite;
      
      private var loadBar:LoadBar = new LoadBar();
      
      private var nowGoods:GoodsData;
      
      public function PostUI()
      {
         super();
         UICn = "职务";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["gripTag","loadBarSp","postTxt","timeTxt","expTxt","lvTxt","closeBtn"];
         super.setImg(img0);
         FontDeal.dealOne(this.postTxt);
         FontDeal.dealOne(this.timeTxt);
         this.gripTag.addChild(this.shopBox);
         this.shopBox.arg.init(1,4,7,7);
         this.shopBox.setIconPro("PostUI/shopGrip");
         this.shopBox.evt.setWantEvent(true,false,false,true,true);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.shopBox);
         this.shopBox.addEventListener(ClickEvent.ON_CLICK,this.goodsClick);
         addChild(this.loadBar);
         this.loadBar.setImg(this.loadBarSp);
         loopMc(img0);
         this.closeBtn.addEventListener(MouseEvent.CLICK,this.closeClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function loopOne(mc0:DisplayObject, name0:String) : Boolean
      {
         var label0:String = null;
         var box0:PostDayBox = null;
         var f0:int = int(name0.indexOf("BoxSp"));
         if(f0 > 0)
         {
            label0 = name0.substr(0,f0);
            box0 = new PostDayBox();
            box0.UILabel = label0;
            addChild(box0);
            box0.setImg(mc0 as Sprite);
            this.boxObj[label0] = box0;
            this[label0 + "Box"] = box0;
            box0.postUI = this;
            return true;
         }
         return false;
      }
      
      override public function show() : void
      {
         Gaming.uiGroup.connectUI.show();
         Gaming.api.save.getServerTime(this.yesGetTime,this.noGetTime);
      }
      
      private function yesGetTime(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         super.show();
         var da0:PostData = Gaming.PG.da.post;
         da0.openUI(timeStr0);
         if(da0.save.tipStr != "")
         {
            Gaming.uiGroup.alertBox.showNormal(da0.save.tipStr,"yes");
            da0.save.tipStr = "";
         }
         this.fleshData();
      }
      
      private function noGetTime(v0:* = null) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("无法获取服务器时间。");
         hide();
      }
      
      public function outLoginEvent() : void
      {
         this.nowGoods = null;
      }
      
      public function fleshData() : void
      {
         this.fleshGoods();
         this.fleshInfo();
         this.fleshPrivilege();
      }
      
      private function getNowTimeStr() : String
      {
         return Gaming.api.save.getNowServerDate().getStr();
      }
      
      private function fleshGoods() : void
      {
         var goodsArr0:Array = null;
         if(this.shopBox.gripArr.length == 0)
         {
            goodsArr0 = Gaming.PG.da.post.getGoodDataArr();
            this.shopBox.inData_byArr(goodsArr0,"inData_goods");
         }
         this.shopBox.doChildFun("fleshGoodsPrice");
      }
      
      private function goodsClick(e:ClickEvent) : void
      {
         var da0:GoodsData = e.childData as GoodsData;
         this.nowGoods = da0;
         var str0:String = Gaming.PG.da.post.buyPan(da0.def.name,this.getNowTimeStr());
         if(str0 != "")
         {
            Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",this.chooseBuy);
         }
         else
         {
            this.chooseBuy();
         }
      }
      
      private function chooseBuy() : void
      {
         Gaming.uiGroup.alertBox.shop.showCheck(this.nowGoods,this.yes_buy);
      }
      
      private function yes_buy() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         Gaming.PG.da.goods.addBuyNum(da0.def.name,da0.nowNum);
         // 统一使用银币支付，不再区分货币类型
         this.do_buy();
      }
      
      private function do_buy() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         if(da0.def.priceType != PriceType.MONEY)
         {
            Gaming.PG.da.useCrrency(price0,da0.def.priceType);
         }
         else
         {
            Gaming.api.shopItemsLog(da0);
         }
         Gaming.PG.da.post.useCard(da0.def.name,da0.getTrueNum(),this.getNowTimeStr());
         Gaming.soundGroup.playSound("uiSound","buy");
         this.fleshData();
         Gaming.uiGroup.mainUI.fleshCoin();
         Gaming.PG.da.fleshAllByEquip();
      }
      
      private function fleshInfo() : void
      {
         var d0:PostDefine = null;
         var surplusDay0:int = 0;
         var da0:PostData = Gaming.PG.da.post;
         var nowPost0:String = da0.save.nowPost;
         var lv0:int = da0.save.postLv;
         if(nowPost0 == "")
         {
            this.postTxt.text = "无职务";
            this.timeTxt.text = "无";
            this.lvTxt.text = "职务等级：无";
         }
         else
         {
            d0 = da0.getNowPostDefine();
            surplusDay0 = da0.save.getSurplusDay(this.getNowTimeStr());
            this.postTxt.text = d0.cnName;
            this.timeTxt.htmlText = "职务剩余时间" + ComMethod.color(surplusDay0 + "","#FF9900") + "天";
            this.lvTxt.text = "职务等级：" + lv0;
         }
         var nowExp0:Number = da0.save.postExp;
         var maxExp0:Number = da0.save.getNowMaxPostExp();
         this.expTxt.text = "职务经验：" + nowExp0 + "/" + maxExp0;
         this.loadBar.setPer(nowExp0 / maxExp0);
      }
      
      private function fleshPrivilege() : void
      {
         var box0:PostDayBox = null;
         var da0:PostData = Gaming.PG.da.post;
         for each(box0 in this.boxObj)
         {
            box0.inData(da0);
         }
      }
      
      private function closeClick(e:MouseEvent) : void
      {
         Gaming.soundGroup.playSound("uiSound","click");
         hide();
      }
   }
}

